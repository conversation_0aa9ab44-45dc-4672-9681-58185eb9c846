import { ValidationResult } from '@/types';

/**
 * 验证股票代码格式
 * @param code 股票代码
 * @returns 验证结果
 */
export function validateStockCode(code: string): ValidationResult {
  // 去除空格
  const trimmedCode = code.trim();
  
  // 检查是否为空
  if (!trimmedCode) {
    return {
      isValid: false,
      message: '股票代码不能为空',
    };
  }
  
  // 检查长度是否为6位
  if (trimmedCode.length !== 6) {
    return {
      isValid: false,
      message: '股票代码必须为6位数字',
    };
  }
  
  // 检查是否全为数字
  if (!/^\d{6}$/.test(trimmedCode)) {
    return {
      isValid: false,
      message: '股票代码只能包含数字',
    };
  }
  
  // 检查是否为有效的股票代码范围
  const codeNum = parseInt(trimmedCode);
  
  // 沪市主板: 600000-699999
  // 深市主板: 000000-099999
  // 深市中小板: 002000-004999
  // 创业板: 300000-399999
  // 科创板: 688000-689999
  // 北交所: 430000-899999
  const isValidRange = 
    (codeNum >= 600000 && codeNum <= 699999) || // 沪市主板
    (codeNum >= 0 && codeNum <= 99999) ||       // 深市主板
    (codeNum >= 2000 && codeNum <= 4999) ||     // 深市中小板
    (codeNum >= 300000 && codeNum <= 399999) || // 创业板
    (codeNum >= 688000 && codeNum <= 689999) || // 科创板
    (codeNum >= 430000 && codeNum <= 899999);   // 北交所
  
  if (!isValidRange) {
    return {
      isValid: false,
      message: '请输入有效的股票代码',
    };
  }
  
  return {
    isValid: true,
  };
}

/**
 * 格式化股票代码（补零）
 * @param code 股票代码
 * @returns 格式化后的代码
 */
export function formatStockCode(code: string): string {
  const trimmedCode = code.trim();
  return trimmedCode.padStart(6, '0');
}

/**
 * 检查股票代码是否重复
 * @param code 要检查的代码
 * @param existingCodes 已存在的代码列表
 * @returns 是否重复
 */
export function isDuplicateCode(code: string, existingCodes: string[]): boolean {
  const formattedCode = formatStockCode(code);
  return existingCodes.includes(formattedCode);
}

/**
 * 批量验证股票代码
 * @param codes 股票代码数组
 * @returns 验证结果数组
 */
export function validateStockCodes(codes: string[]): ValidationResult[] {
  return codes.map(code => validateStockCode(code));
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: number;

  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = window.setTimeout(() => func(...args), delay);
  };
}
