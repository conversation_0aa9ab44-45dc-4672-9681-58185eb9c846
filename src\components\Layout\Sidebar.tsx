import React from 'react';
import { StockManager } from '@/components';
import { useStockList } from '@/hooks/useStockData';
import { X, TrendingUp, Plus, List } from 'lucide-react';

interface SidebarProps {
  /** 选中的股票代码 */
  selectedStock: string | null;
  /** 股票选择事件 */
  onStockSelect: (code: string) => void;
  /** 是否显示侧边栏（移动端） */
  isOpen?: boolean;
  /** 关闭侧边栏事件（移动端） */
  onClose?: () => void;
  /** 自定义类名 */
  className?: string;
}

/**
 * 侧边栏组件
 */
export const Sidebar: React.FC<SidebarProps> = ({
  selectedStock,
  onStockSelect,
  isOpen = true,
  onClose,
  className = '',
}) => {
  const { stocks, isLoading, error } = useStockList();

  // 快速添加常用股票
  const popularStocks = [
    { code: '600121', name: '郑州煤电' },
    { code: '000001', name: '平安银行' },
    { code: '000002', name: '万科A' },
    { code: '600036', name: '招商银行' },
    { code: '000858', name: '五粮液' },
  ];

  return (
    <>
      {/* 移动端遮罩层 */}
      {isOpen && onClose && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* 侧边栏内容 */}
      <aside 
        className={`
          fixed lg:static inset-y-0 left-0 z-50 lg:z-auto
          w-80 bg-white border-r border-gray-200
          transform transition-transform duration-300 ease-in-out
          ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
          ${className}
        `}
      >
        {/* 侧边栏头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 lg:hidden">
          <h2 className="text-lg font-semibold text-gray-900">股票管理</h2>
          {onClose && (
            <button
              onClick={onClose}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              aria-label="关闭菜单"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>

        {/* 侧边栏内容 */}
        <div className="flex flex-col h-full lg:h-auto">
          {/* 股票管理组件 */}
          <div className="flex-1 p-4">
            <StockManager
              selectedStock={selectedStock}
              onSelectStock={onStockSelect}
            />
          </div>

          {/* 快速添加区域 */}
          <div className="border-t border-gray-200 p-4">
            <div className="mb-3">
              <h3 className="text-sm font-medium text-gray-900 mb-2 flex items-center gap-2">
                <TrendingUp className="w-4 h-4" />
                热门股票
              </h3>
              <p className="text-xs text-gray-500">
                点击快速添加到监控列表
              </p>
            </div>

            <div className="space-y-2">
              {popularStocks.map((stock) => {
                const isAdded = stocks.some(s => s.code === stock.code);
                const isSelected = selectedStock === stock.code;
                
                return (
                  <button
                    key={stock.code}
                    onClick={() => {
                      if (!isAdded) {
                        // 这里需要调用添加股票的方法
                        // 暂时只是选择
                        onStockSelect(stock.code);
                      } else {
                        onStockSelect(stock.code);
                      }
                    }}
                    className={`
                      w-full text-left p-2 rounded-lg border transition-colors
                      ${isSelected 
                        ? 'border-primary-200 bg-primary-50 text-primary-700' 
                        : isAdded
                        ? 'border-green-200 bg-green-50 text-green-700 hover:bg-green-100'
                        : 'border-gray-200 text-gray-700 hover:bg-gray-50'
                      }
                    `}
                    disabled={isLoading}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-sm font-medium">
                          {stock.name}
                        </div>
                        <div className="text-xs opacity-75">
                          {stock.code}
                        </div>
                      </div>
                      {!isAdded && (
                        <Plus className="w-4 h-4 opacity-50" />
                      )}
                      {isSelected && (
                        <div className="w-2 h-2 bg-primary-600 rounded-full" />
                      )}
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          {/* 统计信息 */}
          <div className="border-t border-gray-200 p-4 bg-gray-50">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2 text-gray-600">
                <List className="w-4 h-4" />
                <span>监控股票</span>
              </div>
              <div className="font-medium text-gray-900">
                {stocks.length} 只
              </div>
            </div>
            
            {selectedStock && (
              <div className="mt-2 text-xs text-gray-500">
                当前选中: {selectedStock}
              </div>
            )}

            {error && (
              <div className="mt-2 text-xs text-red-600">
                加载失败: {error.message}
              </div>
            )}
          </div>
        </div>
      </aside>
    </>
  );
};
