// Cloudflare Workers API类型定义

// 环境变量接口
export interface Env extends Record<string, any> {
  STOCK_CACHE: KVNamespace;
  STOCK_CONFIG: KVNamespace;
  ENVIRONMENT: string;
  API_BASE_URL: string;
}

// API响应基础格式
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  timestamp: string;
}

// 错误响应格式
export interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}

// 股票信息
export interface StockInfo {
  code: string;
  name: string;
  addedAt: string;
}

// 东方财富API响应数据结构
export interface EastmoneyApiResponse {
  rc: number;
  rt: number;
  svr: number;
  lt: number;
  full: number;
  dlmkts?: string;
  data: {
    code: string;
    market: number;
    name: string;
    decimal?: number;
    dktotal?: number;
    qtotal?: number;
    klines: string[];
    tradePeriods?: {
      pre?: { b: number; e: number };
      after?: { b: number; e: number } | null;
      periods: Array<{ b: number; e: number }>;
    };
    [key: string]: any;
  };
}

// 处理后的资金流向数据
export interface ProcessedFlowData {
  summary: {
    code: string;
    name: string;
    market: number;
    lastUpdate: string;
    mainNetInflow: number;
    superLargeNetInflow: number;
    largeNetInflow: number;
    mediumNetInflow: number;
    smallNetInflow: number;
  } | null;
  klines: Array<{
    time: string;
    mainNetInflow: number;
    superLargeNetInflow: number;
    largeNetInflow: number;
    mediumNetInflow: number;
    smallNetInflow: number;
  }>;
  totalCount: number;
  tradePeriods?: any;
}

// 股票资金流向汇总数据
export interface StockFlowSummary {
  code: string;
  name: string;
  market: number;
  lastUpdate: string;
  mainNetInflow: number;
  mainNetRatio?: number;
  superLargeNetInflow: number;
  superLargeNetRatio?: number;
  largeNetInflow: number;
  largeNetRatio?: number;
  mediumNetInflow: number;
  mediumNetRatio?: number;
  smallNetInflow: number;
  smallNetRatio?: number;
}

// 缓存键类型
export type CacheKey = 
  | `stock_data:${string}`
  | `stock_list`
  | `last_update:${string}`;

// 请求头配置
export type RequestHeaders = Record<string, string>;
