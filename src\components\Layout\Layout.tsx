import React, { useState, useEffect } from 'react';
import { Header } from './Header';
import { Sidebar } from './Sidebar';
import { MainContent } from './MainContent';
import { StockTabs } from '../DataDisplay/StockTabs';
import { useStockList } from '@/hooks/useStockData';

interface LayoutProps {
  /** 子组件 */
  children?: React.ReactNode;
}

/**
 * 应用主布局组件
 */
export const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [selectedStock, setSelectedStock] = useState<string | null>(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isDark, setIsDark] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  const { stocks } = useStockList();

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 自动选择第一个股票
  useEffect(() => {
    if (stocks.length > 0 && !selectedStock) {
      setSelectedStock(stocks[0].code);
    }
  }, [stocks, selectedStock]);

  // 移动端自动关闭侧边栏
  useEffect(() => {
    if (!isMobile) {
      setIsSidebarOpen(false);
    }
  }, [isMobile]);

  // 处理股票选择
  const handleStockSelect = (code: string) => {
    setSelectedStock(code);
    // 移动端选择后关闭侧边栏
    if (isMobile) {
      setIsSidebarOpen(false);
    }
  };

  // 处理主题切换
  const handleThemeToggle = () => {
    setIsDark(!isDark);
    // 这里可以添加主题持久化逻辑
    document.documentElement.classList.toggle('dark', !isDark);
  };

  return (
    <div className={`min-h-screen bg-gray-50 ${isDark ? 'dark' : ''}`}>
      {/* 页面头部 */}
      <Header
        showMenuButton={isMobile}
        onMenuClick={() => setIsSidebarOpen(true)}
        isDark={isDark}
        onThemeToggle={handleThemeToggle}
      />

      {/* 主要内容区域 */}
      <div className="flex h-[calc(100vh-64px)]">
        {/* 侧边栏 */}
        <Sidebar
          selectedStock={selectedStock}
          onStockSelect={handleStockSelect}
          isOpen={isMobile ? isSidebarOpen : true}
          onClose={isMobile ? () => setIsSidebarOpen(false) : undefined}
        />

        {/* 主内容区 */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* 股票标签页 */}
          {stocks.length > 0 && (
            <StockTabs
              selectedStock={selectedStock}
              onStockSelect={handleStockSelect}
              maxTabs={isMobile ? 3 : 8}
            />
          )}

          {/* 主内容 */}
          {children || (
            <MainContent
              selectedStock={selectedStock}
            />
          )}
        </div>
      </div>
    </div>
  );
};
