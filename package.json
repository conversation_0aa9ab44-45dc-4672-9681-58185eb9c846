{"name": "gupiao-zijinliu", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:prod": "tsc && vite build --mode production", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "type-check": "tsc --noEmit", "deploy:preview": "npm run build && npx wrangler pages publish dist --project-name gupiao-zijin<PERSON>u", "deploy:prod": "npm run build:prod && npx wrangler pages publish dist --project-name gupiao-zijinliu --env production"}, "dependencies": {"clsx": "^2.0.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-query": "^3.39.3"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "terser": "^5.43.1", "typescript": "^5.2.2", "vite": "^5.0.8"}}