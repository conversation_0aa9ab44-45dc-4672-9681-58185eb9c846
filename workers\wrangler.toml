#:schema node_modules/wrangler/config-schema.json
name = "gupiao-zijinliu-api"
main = "src/index.ts"
compatibility_date = "2023-12-18"
compatibility_flags = ["nodejs_compat"]

# KV命名空间配置
[[kv_namespaces]]
binding = "STOCK_CACHE"
id = "stock_cache_namespace"
preview_id = "stock_cache_preview"

[[kv_namespaces]]
binding = "STOCK_CONFIG"
id = "stock_config_namespace"
preview_id = "stock_config_preview"

# 定时任务配置 - 每分钟执行一次
[triggers]
crons = ["* * * * *"]

# 环境变量
[vars]
ENVIRONMENT = "development"
API_BASE_URL = "https://push2.eastmoney.com"
CRON_ENABLED = "true"
CACHE_TTL = "60"
BATCH_SIZE = "10"
MAX_RETRIES = "3"
LOG_LEVEL = "info"

# 开发环境配置
[env.development]
name = "gupiao-zijinliu-api-dev"

[env.development.vars]
ENVIRONMENT = "development"
API_BASE_URL = "https://push2.eastmoney.com"
CRON_ENABLED = "true"
CACHE_TTL = "60"
BATCH_SIZE = "5"
MAX_RETRIES = "3"
LOG_LEVEL = "debug"

# 生产环境配置
[env.production]
name = "gupiao-zijinliu-api-prod"

[env.production.vars]
ENVIRONMENT = "production"
API_BASE_URL = "https://push2.eastmoney.com"
CRON_ENABLED = "true"
CACHE_TTL = "60"
BATCH_SIZE = "10"
MAX_RETRIES = "3"
LOG_LEVEL = "info"
