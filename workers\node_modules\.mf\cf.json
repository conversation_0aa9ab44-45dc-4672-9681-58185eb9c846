{"clientTcpRtt": 151, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "AS", "asn": 4134, "clientAcceptEncoding": "gzip, deflate, br", "verifiedBotCategory": "", "country": "CN", "isEUCountry": false, "region": "Guangdong", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "fhUO59oXsmqHInRW/igBB+o/jfWzwAEyFBVIDbC6TU8=", "tlsExportedAuthenticator": {"clientFinished": "079d3c92ff953f15974b04376d45d0dd7affb3f5933838ed2a9c7a43ad7a0e7cb117266ba7e27d17267bb8c06073a743", "clientHandshake": "148c7e59b03e0a916c6416efab04676a9bfc5f28add9e8ec53fcb7abdac9abde36c28edab21492037ea633853ff01b5d", "serverHandshake": "4ea6b729e4acd2c5b5cce80bd6d79e0cd1a063f7b63d06e7c6261929175e20f07f2252734440c4eff3a06d5736660127", "serverFinished": "952083faa6266262ad9608d749f1248709d503ee811176abef11eacf4c2fcd9c65aadcded23e3d401bdb60d750471fc8"}, "tlsClientHelloLength": "386", "colo": "SJC", "timezone": "Asia/Shanghai", "longitude": "114.06830", "latitude": "22.54554", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "518000", "city": "Shenzhen", "tlsVersion": "TLSv1.3", "regionCode": "GD", "asOrganization": "CHINANET Guangdong province network", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}