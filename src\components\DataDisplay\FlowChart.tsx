import React, { useMemo, useRef, useEffect, useState } from 'react';
import ReactECharts from 'echarts-for-react';
import { EChartsOption } from 'echarts';
import { 
  createFlowLineChartConfig, 
  createFlowBarChartConfig, 
  getResponsiveConfig 
} from '@/utils/chartConfig';
import { KlineDataPoint, StockFlowSummary } from '@/types/stock';
import { BarChart3, TrendingUp, Maximize2, Minimize2 } from 'lucide-react';

interface FlowChartProps {
  /** K线数据 */
  klines: KlineDataPoint[];
  /** 汇总数据 */
  summary: StockFlowSummary | null;
  /** 图表类型 */
  type?: 'line' | 'bar';
  /** 是否为暗色主题 */
  isDark?: boolean;
  /** 图表高度 */
  height?: number;
  /** 是否显示工具栏 */
  showToolbar?: boolean;
  /** 加载状态 */
  loading?: boolean;
  /** 错误信息 */
  error?: string | null;
  /** 自定义类名 */
  className?: string;
}

/**
 * 资金流向图表组件
 */
export const FlowChart: React.FC<FlowChartProps> = ({
  klines = [],
  summary = null,
  type = 'line',
  isDark = false,
  height = 400,
  showToolbar = true,
  loading = false,
  error = null,
  className = '',
}) => {
  const chartRef = useRef<ReactECharts>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [chartType, setChartType] = useState<'line' | 'bar'>(type);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [containerWidth, setContainerWidth] = useState(800);

  // 监听容器尺寸变化
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth);
      }
    };

    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  // 图表配置
  const chartOption = useMemo((): EChartsOption => {
    if (chartType === 'line' && klines.length > 0) {
      const lineConfig = createFlowLineChartConfig(klines, isDark);
      const responsiveConfig = getResponsiveConfig(containerWidth);
      return { ...lineConfig, ...responsiveConfig };
    } else if (chartType === 'bar' && summary) {
      const barConfig = createFlowBarChartConfig(summary, isDark);
      const responsiveConfig = getResponsiveConfig(containerWidth);
      return { ...barConfig, ...responsiveConfig };
    }
    
    return {};
  }, [chartType, klines, summary, isDark, containerWidth]);

  // 切换图表类型
  const handleTypeChange = (newType: 'line' | 'bar') => {
    setChartType(newType);
  };

  // 切换全屏
  const handleFullscreenToggle = () => {
    setIsFullscreen(!isFullscreen);
  };

  // 刷新图表
  const handleRefresh = () => {
    if (chartRef.current) {
      const chartInstance = chartRef.current.getEchartsInstance();
      chartInstance.resize();
    }
  };

  // 导出图表
  const handleExport = () => {
    if (chartRef.current) {
      const chartInstance = chartRef.current.getEchartsInstance();
      const dataURL = chartInstance.getDataURL({
        type: 'png',
        backgroundColor: isDark ? '#1f2937' : '#ffffff',
      });
      
      const link = document.createElement('a');
      link.download = `资金流向图表_${new Date().toISOString().slice(0, 10)}.png`;
      link.href = dataURL;
      link.click();
    }
  };

  // 渲染加载状态
  if (loading) {
    return (
      <div 
        className={`flex items-center justify-center bg-white rounded-lg border ${className}`}
        style={{ height }}
      >
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
          <p className="text-gray-600">加载图表数据中...</p>
        </div>
      </div>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <div 
        className={`flex items-center justify-center bg-white rounded-lg border ${className}`}
        style={{ height }}
      >
        <div className="text-center">
          <div className="text-red-500 mb-2">
            <BarChart3 className="w-8 h-8 mx-auto" />
          </div>
          <p className="text-red-600 font-medium">图表加载失败</p>
          <p className="text-gray-500 text-sm mt-1">{error}</p>
        </div>
      </div>
    );
  }

  // 渲染无数据状态
  if (klines.length === 0 && !summary) {
    return (
      <div 
        className={`flex items-center justify-center bg-white rounded-lg border ${className}`}
        style={{ height }}
      >
        <div className="text-center">
          <div className="text-gray-400 mb-2">
            <BarChart3 className="w-8 h-8 mx-auto" />
          </div>
          <p className="text-gray-600">暂无图表数据</p>
          <p className="text-gray-500 text-sm mt-1">请选择股票查看资金流向</p>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef}
      className={`bg-white rounded-lg border ${isFullscreen ? 'fixed inset-0 z-50' : ''} ${className}`}
    >
      {/* 工具栏 */}
      {showToolbar && (
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <h3 className="text-lg font-semibold text-gray-900">资金流向图表</h3>
            {summary && (
              <span className="text-sm text-gray-500">
                {summary.name} ({summary.code})
              </span>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {/* 图表类型切换 */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => handleTypeChange('line')}
                className={`px-3 py-1 rounded text-sm transition-colors ${
                  chartType === 'line'
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                disabled={klines.length === 0}
              >
                <TrendingUp className="w-4 h-4 inline mr-1" />
                趋势图
              </button>
              <button
                onClick={() => handleTypeChange('bar')}
                className={`px-3 py-1 rounded text-sm transition-colors ${
                  chartType === 'bar'
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                disabled={!summary}
              >
                <BarChart3 className="w-4 h-4 inline mr-1" />
                柱状图
              </button>
            </div>
            
            {/* 操作按钮 */}
            <button
              onClick={handleRefresh}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
              title="刷新图表"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
            
            <button
              onClick={handleExport}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
              title="导出图表"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </button>
            
            <button
              onClick={handleFullscreenToggle}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
              title={isFullscreen ? "退出全屏" : "全屏显示"}
            >
              {isFullscreen ? (
                <Minimize2 className="w-4 h-4" />
              ) : (
                <Maximize2 className="w-4 h-4" />
              )}
            </button>
          </div>
        </div>
      )}
      
      {/* 图表内容 */}
      <div className="p-4">
        <ReactECharts
          ref={chartRef}
          option={chartOption}
          style={{ 
            height: isFullscreen ? 'calc(100vh - 120px)' : height,
            width: '100%' 
          }}
          notMerge={true}
          lazyUpdate={true}
          theme={isDark ? 'dark' : undefined}
        />
      </div>
    </div>
  );
};
