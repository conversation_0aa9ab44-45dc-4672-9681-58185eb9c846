import { QueryClient, QueryClientConfig } from 'react-query';

/**
 * React Query 配置选项
 */
const queryClientConfig: QueryClientConfig = {
  defaultOptions: {
    queries: {
      // 缓存时间：5分钟
      staleTime: 5 * 60 * 1000,
      // 缓存保持时间：10分钟
      cacheTime: 10 * 60 * 1000,
      // 重试配置
      retry: (failureCount, error) => {
        // 对于4xx错误不重试
        if (error instanceof Error && 'status' in error) {
          const status = (error as any).status;
          if (status >= 400 && status < 500) {
            return false;
          }
        }
        // 最多重试2次
        return failureCount < 2;
      },
      // 重试延迟：指数退避
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      // 窗口聚焦时不自动重新获取
      refetchOnWindowFocus: false,
      // 网络重连时重新获取
      refetchOnReconnect: true,
      // 组件挂载时不自动重新获取（除非数据过期）
      refetchOnMount: true,
    },
    mutations: {
      // 变更重试配置
      retry: 1,
      retryDelay: 1000,
    },
  },
};

/**
 * 创建 Query Client 实例
 */
export const queryClient = new QueryClient(queryClientConfig);

/**
 * Query Keys 常量
 */
export const QUERY_KEYS = {
  // 股票相关
  STOCKS: ['stocks'] as const,
  STOCK_LIST: ['stocks', 'list'] as const,
  
  // 股票数据相关
  STOCK_DATA: (code: string) => ['stockData', code] as const,
  STOCK_DATA_BATCH: (codes: string[]) => ['stockData', 'batch', codes.sort().join(',')] as const,
  STOCK_LAST_UPDATE: (code: string) => ['stockData', code, 'lastUpdate'] as const,
  
  // API状态
  API_STATUS: ['api', 'status'] as const,
} as const;

/**
 * 查询选项预设
 */
export const QUERY_OPTIONS = {
  // 股票列表查询选项
  STOCK_LIST: {
    staleTime: 30 * 60 * 1000, // 30分钟
    cacheTime: 60 * 60 * 1000, // 1小时
  },
  
  // 股票数据查询选项
  STOCK_DATA: {
    staleTime: 1 * 60 * 1000, // 1分钟
    cacheTime: 5 * 60 * 1000, // 5分钟
    refetchInterval: 60 * 1000, // 每分钟自动刷新
    refetchIntervalInBackground: false, // 后台不刷新
  },
  
  // 批量股票数据查询选项
  STOCK_DATA_BATCH: {
    staleTime: 1 * 60 * 1000, // 1分钟
    cacheTime: 5 * 60 * 1000, // 5分钟
    refetchInterval: 60 * 1000, // 每分钟自动刷新
  },
  
  // API状态查询选项
  API_STATUS: {
    staleTime: 30 * 1000, // 30秒
    cacheTime: 2 * 60 * 1000, // 2分钟
    refetchInterval: 30 * 1000, // 每30秒刷新
  },
} as const;

/**
 * 清除所有缓存
 */
export const clearAllCache = () => {
  queryClient.clear();
};

/**
 * 清除特定股票的缓存
 * @param code 股票代码
 */
export const clearStockCache = (code: string) => {
  queryClient.removeQueries(QUERY_KEYS.STOCK_DATA(code));
  queryClient.removeQueries(QUERY_KEYS.STOCK_LAST_UPDATE(code));
};

/**
 * 预取股票数据
 * @param code 股票代码
 * @param limit 数据条数
 */
export const prefetchStockData = async (code: string, limit: number = 240) => {
  const { getStockData } = await import('@/services/stockApi');
  
  await queryClient.prefetchQuery(
    QUERY_KEYS.STOCK_DATA(code),
    () => getStockData(code, limit),
    QUERY_OPTIONS.STOCK_DATA
  );
};

/**
 * 使查询失效并重新获取
 * @param queryKey 查询键
 */
export const invalidateQuery = (queryKey: readonly unknown[]) => {
  queryClient.invalidateQueries(queryKey);
};

/**
 * 手动设置查询数据
 * @param queryKey 查询键
 * @param data 数据
 */
export const setQueryData = <T>(queryKey: readonly unknown[], data: T) => {
  queryClient.setQueryData(queryKey, data);
};

/**
 * 获取查询数据
 * @param queryKey 查询键
 * @returns 缓存的数据
 */
export const getQueryData = <T>(queryKey: readonly unknown[]): T | undefined => {
  return queryClient.getQueryData<T>(queryKey);
};

/**
 * 网络状态检测
 */
export const isOnline = () => {
  return navigator.onLine;
};

/**
 * 离线状态处理
 */
export const handleOfflineMode = () => {
  if (!isOnline()) {
    // 停止所有自动刷新
    queryClient.getQueryCache().getAll().forEach(query => {
      // 暂停查询的自动刷新
      query.cancel();
    });
  }
};

/**
 * 在线状态恢复
 */
export const handleOnlineMode = () => {
  if (isOnline()) {
    // 恢复自动刷新并重新获取数据
    queryClient.invalidateQueries();
  }
};
