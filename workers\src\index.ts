import { Hono } from 'hono';
import { EastmoneyApiService } from './services/eastmoneyApi';
import { StocksHandler } from './handlers/stocks';
import { DataHandler } from './handlers/data';
import { CronService } from './services/cronService';
import { getEnvironmentCors } from './middleware/cors';
import { createLogger } from './utils/logger';
import { Env } from './types/api';

const app = new Hono<{
  Bindings: Env;
  Variables: {
    stocksHandler: StocksHandler;
    dataHandler: DataHandler;
  };
}>();

// 动态CORS配置
app.use('*', async (c, next) => {
  const environment = c.env?.ENVIRONMENT || 'development';
  const corsMiddleware = getEnvironmentCors(environment);
  return corsMiddleware(c, next);
});

// 请求日志中间件
app.use('*', async (c, next) => {
  const start = Date.now();
  const method = c.req.method;
  const url = c.req.url;

  await next();

  const duration = Date.now() - start;
  const status = c.res.status;

  console.log(`${method} ${url} - ${status} (${duration}ms)`);
});

// 创建API服务实例
const apiService = new EastmoneyApiService();

// 创建日志记录器
const logger = createLogger('MainWorker');

// 健康检查端点
app.get('/health', (c) => {
  const serviceStatus = apiService.getServiceStatus();
  return c.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: c.env?.ENVIRONMENT || 'unknown',
    apiService: serviceStatus,
    version: '1.0.0',
  });
});

// 创建处理器实例的中间件
app.use('/api/*', async (c, next) => {
  // 将处理器实例添加到上下文中
  c.set('stocksHandler', new StocksHandler(c.env));
  c.set('dataHandler', new DataHandler(c.env));
  await next();
});

// === 股票管理API ===

// 获取股票列表
app.get('/api/stocks', async (c) => {
  const handler = c.get('stocksHandler') as StocksHandler;
  return handler.getStocks(c);
});

// 添加股票
app.post('/api/stocks', async (c) => {
  const handler = c.get('stocksHandler') as StocksHandler;
  return handler.addStock(c);
});

// 批量添加股票
app.post('/api/stocks/batch', async (c) => {
  const handler = c.get('stocksHandler') as StocksHandler;
  return handler.addStocksBatch(c);
});

// 删除股票
app.delete('/api/stocks/:code', async (c) => {
  const handler = c.get('stocksHandler') as StocksHandler;
  return handler.deleteStock(c);
});

// 清空所有股票
app.delete('/api/stocks', async (c) => {
  const handler = c.get('stocksHandler') as StocksHandler;
  return handler.clearAllStocks(c);
});

// === 数据获取API ===

// 获取单个股票数据
app.get('/api/data/:code', async (c) => {
  const handler = c.get('dataHandler') as DataHandler;
  return handler.getStockData(c);
});

// 批量获取股票数据
app.get('/api/data/batch', async (c) => {
  const handler = c.get('dataHandler') as DataHandler;
  return handler.getBatchStockData(c);
});

// 获取股票最后更新时间
app.get('/api/data/:code/last-update', async (c) => {
  const handler = c.get('dataHandler') as DataHandler;
  return handler.getLastUpdate(c);
});

// 清除股票数据缓存
app.delete('/api/data/:code/cache', async (c) => {
  const handler = c.get('dataHandler') as DataHandler;
  return handler.clearCache(c);
});

// 获取API服务状态
app.get('/api/data/status', async (c) => {
  const handler = c.get('dataHandler') as DataHandler;
  return handler.getServiceStatus(c);
});

// === 定时任务API ===

// 获取定时任务状态
app.get('/api/cron/status', async (c) => {
  try {
    const cronService = new CronService(c.env);
    const status = await cronService.getTaskStatus();
    return c.json({
      success: true,
      data: status,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to get cron status', { error: (error as Error).message });
    return c.json({
      success: false,
      message: '获取定时任务状态失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// 手动触发定时任务
app.post('/api/cron/trigger', async (c) => {
  try {
    const cronService = new CronService(c.env);
    const result = await cronService.triggerManualTask();
    return c.json({
      success: true,
      data: result,
      message: '定时任务执行完成',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Failed to trigger cron task', { error: (error as Error).message });
    return c.json({
      success: false,
      message: '触发定时任务失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// === 测试和工具API ===

// API测试端点
app.get('/api/test', async (c) => {
  try {
    const testResult = await apiService.getStockFlowData('600121', 5);
    return c.json({
      message: 'API测试完成',
      testResult,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return c.json({
      message: 'API测试失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// 获取API文档
app.get('/api/docs', (c) => {
  return c.json({
    title: '股票资金流向监控 API 文档',
    version: '1.0.0',
    baseUrl: c.req.url.replace(/\/api\/docs.*$/, ''),
    endpoints: {
      stocks: {
        'GET /api/stocks': '获取股票列表',
        'POST /api/stocks': '添加股票 { code: string, name?: string }',
        'POST /api/stocks/batch': '批量添加股票 { stocks: Array<{code, name}> }',
        'DELETE /api/stocks/:code': '删除指定股票',
        'DELETE /api/stocks': '清空所有股票',
      },
      data: {
        'GET /api/data/:code': '获取股票数据 ?limit=240&cache=true',
        'GET /api/data/batch': '批量获取数据 ?codes=600121,000001&limit=240',
        'GET /api/data/:code/last-update': '获取最后更新时间',
        'DELETE /api/data/:code/cache': '清除股票缓存',
        'GET /api/data/status': '获取API服务状态',
      },
      cron: {
        'GET /api/cron/status': '获取定时任务状态',
        'POST /api/cron/trigger': '手动触发定时任务',
      },
      utils: {
        'GET /health': '健康检查',
        'GET /api/test': 'API测试',
        'GET /api/docs': 'API文档',
      },
    },
    examples: {
      addStock: 'POST /api/stocks\n{ "code": "600121", "name": "郑州煤电" }',
      getStockData: 'GET /api/data/600121?limit=100',
      batchGetData: 'GET /api/data/batch?codes=600121,000001&limit=50',
      cronStatus: 'GET /api/cron/status',
      triggerCron: 'POST /api/cron/trigger',
    },
  });
});

// 根路径
app.get('/', (c) => {
  return c.json({
    message: '股票资金流向监控 API',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    environment: c.env?.ENVIRONMENT || 'unknown',
    quickStart: {
      documentation: '/api/docs',
      health: '/health',
      test: '/api/test',
    },
    features: [
      '股票代码管理',
      '实时资金流向数据获取',
      '批量数据处理',
      '智能缓存机制',
      'RESTful API设计',
    ],
  });
});

// 404处理
app.notFound((c) => {
  return c.json({
    success: false,
    message: '接口不存在',
    availableEndpoints: '/api/docs',
    timestamp: new Date().toISOString(),
  }, 404);
});

// 错误处理
app.onError((err, c) => {
  logger.error('API error', { error: err.message, stack: err.stack });
  return c.json({
    success: false,
    message: '服务器内部错误',
    error: err.message,
    timestamp: new Date().toISOString(),
  }, 500);
});

// 定时任务处理器
export default {
  fetch: app.fetch,

  // Cron触发器处理
  async scheduled(event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void> {
    const cronLogger = createLogger('CronHandler', env);
    cronLogger.info('Scheduled event triggered', {
      scheduledTime: new Date(event.scheduledTime).toISOString(),
      cron: event.cron
    });

    try {
      const cronService = new CronService(env);
      const result = await cronService.executeScheduledTask();

      cronLogger.info('Scheduled task completed', result);

      // 记录执行时间到缓存
      const cacheService = new (await import('./services/cache')).CacheService(env.STOCK_CACHE);
      await cacheService.set('last_cron_execution', new Date().toISOString(), 86400);
    } catch (error) {
      cronLogger.error('Scheduled task failed', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  },
};
