import { Context } from 'hono';
import { EastmoneyApiService } from '../services/eastmoneyApi';
import { CacheService } from '../services/cache';
import { ApiResponse, Env, ProcessedFlowData } from '../types/api';
import { validateStockCode, sanitizeStockCode } from '../services/validator';

/**
 * 数据获取API处理器
 */
export class DataHandler {
  private apiService: EastmoneyApiService;
  private cache: CacheService;

  constructor(env: Env) {
    this.apiService = new EastmoneyApiService();
    this.cache = new CacheService(env.STOCK_CACHE);
  }

  /**
   * 获取单个股票数据
   * GET /api/data/:code
   */
  async getStockData(c: Context): Promise<Response> {
    try {
      const code = c.req.param('code');
      const limit = parseInt(c.req.query('limit') || '240');
      const useCache = c.req.query('cache') !== 'false';

      if (!code) {
        return c.json({
          success: false,
          message: '股票代码不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const cleanCode = sanitizeStockCode(code);
      if (!validateStockCode(cleanCode)) {
        return c.json({
          success: false,
          message: '无效的股票代码格式',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      if (limit < 1 || limit > 1000) {
        return c.json({
          success: false,
          message: '数据条数限制在1-1000之间',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const cacheKey = CacheService.getStockDataKey(cleanCode);

      // 尝试从缓存获取数据
      if (useCache) {
        const cachedData = await this.cache.get<ApiResponse<ProcessedFlowData>>(cacheKey);
        if (cachedData) {
          return c.json({
            ...cachedData,
            fromCache: true,
          });
        }
      }

      // 从API获取数据
      const result = await this.apiService.getStockFlowData(cleanCode, limit);

      // 缓存成功的结果
      if (result.success) {
        await this.cache.set(cacheKey, result, 60); // 1分钟缓存
        await this.cache.set(CacheService.getLastUpdateKey(cleanCode), new Date().toISOString(), 300); // 5分钟缓存
      }

      return c.json(result, result.success ? 200 : 500);
    } catch (error) {
      console.error('获取股票数据失败:', error);
      return c.json({
        success: false,
        message: '服务器内部错误',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 批量获取股票数据
   * GET /api/data/batch
   */
  async getBatchStockData(c: Context): Promise<Response> {
    try {
      const codesParam = c.req.query('codes');
      const limit = parseInt(c.req.query('limit') || '240');
      const useCache = c.req.query('cache') !== 'false';

      if (!codesParam) {
        return c.json({
          success: false,
          message: '股票代码列表不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const codes = codesParam.split(',').map(code => sanitizeStockCode(code.trim())).filter(Boolean);

      if (codes.length === 0) {
        return c.json({
          success: false,
          message: '有效的股票代码不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      if (codes.length > 20) {
        return c.json({
          success: false,
          message: '一次最多只能查询20个股票',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      // 验证所有股票代码
      const invalidCodes = codes.filter(code => !validateStockCode(code));
      if (invalidCodes.length > 0) {
        return c.json({
          success: false,
          message: `无效的股票代码: ${invalidCodes.join(', ')}`,
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const results: Record<string, any> = {};
      const errors: Record<string, string> = {};
      const fromCache: string[] = [];

      // 并发获取数据
      await Promise.all(codes.map(async (code) => {
        try {
          const cacheKey = CacheService.getStockDataKey(code);
          
          // 尝试从缓存获取
          if (useCache) {
            const cachedData = await this.cache.get<ApiResponse<ProcessedFlowData>>(cacheKey);
            if (cachedData && cachedData.success) {
              results[code] = cachedData.data;
              fromCache.push(code);
              return;
            }
          }

          // 从API获取
          const result = await this.apiService.getStockFlowData(code, limit);
          
          if (result.success) {
            results[code] = result.data;
            // 缓存结果
            await this.cache.set(cacheKey, result, 60);
            await this.cache.set(CacheService.getLastUpdateKey(code), new Date().toISOString(), 300);
          } else {
            errors[code] = result.message || '获取失败';
          }
        } catch (error) {
          errors[code] = error instanceof Error ? error.message : '未知错误';
        }
      }));

      const successCount = Object.keys(results).length;
      const errorCount = Object.keys(errors).length;

      return c.json({
        success: successCount > 0,
        data: {
          results,
          errors,
          summary: {
            total: codes.length,
            success: successCount,
            failed: errorCount,
            fromCache: fromCache.length,
          },
        },
        message: `成功获取 ${successCount} 个股票数据${errorCount > 0 ? `，${errorCount} 个失败` : ''}`,
        timestamp: new Date().toISOString(),
      } as ApiResponse, successCount > 0 ? 200 : 500);
    } catch (error) {
      console.error('批量获取股票数据失败:', error);
      return c.json({
        success: false,
        message: '服务器内部错误',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 获取股票最后更新时间
   * GET /api/data/:code/last-update
   */
  async getLastUpdate(c: Context): Promise<Response> {
    try {
      const code = c.req.param('code');
      
      if (!code) {
        return c.json({
          success: false,
          message: '股票代码不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const cleanCode = sanitizeStockCode(code);
      if (!validateStockCode(cleanCode)) {
        return c.json({
          success: false,
          message: '无效的股票代码格式',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const lastUpdate = await this.cache.get<string>(CacheService.getLastUpdateKey(cleanCode));

      return c.json({
        success: true,
        data: {
          code: cleanCode,
          lastUpdate: lastUpdate || null,
          hasData: lastUpdate !== null,
        },
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    } catch (error) {
      console.error('获取最后更新时间失败:', error);
      return c.json({
        success: false,
        message: '服务器内部错误',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 清除股票数据缓存
   * DELETE /api/data/:code/cache
   */
  async clearCache(c: Context): Promise<Response> {
    try {
      const code = c.req.param('code');
      
      if (!code) {
        return c.json({
          success: false,
          message: '股票代码不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const cleanCode = sanitizeStockCode(code);
      if (!validateStockCode(cleanCode)) {
        return c.json({
          success: false,
          message: '无效的股票代码格式',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      // 清除相关缓存
      await Promise.all([
        this.cache.delete(CacheService.getStockDataKey(cleanCode)),
        this.cache.delete(CacheService.getLastUpdateKey(cleanCode)),
      ]);

      return c.json({
        success: true,
        data: { code: cleanCode },
        message: '缓存清除成功',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    } catch (error) {
      console.error('清除缓存失败:', error);
      return c.json({
        success: false,
        message: '服务器内部错误',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 获取API服务状态
   * GET /api/data/status
   */
  async getServiceStatus(c: Context): Promise<Response> {
    try {
      const status = this.apiService.getServiceStatus();
      
      return c.json({
        success: true,
        data: {
          ...status,
          timestamp: new Date().toISOString(),
        },
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    } catch (error) {
      console.error('获取服务状态失败:', error);
      return c.json({
        success: false,
        message: '服务器内部错误',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }
}
