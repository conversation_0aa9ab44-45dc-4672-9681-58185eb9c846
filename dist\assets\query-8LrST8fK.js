import{a as t,R as e}from"./vendor-CykFposD.js";function n(t,e){return(n=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function i(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,n(t,e)}var r=function(){function t(){this.listeners=[]}var e=t.prototype;return e.subscribe=function(t){var e=this,n=t||function(){};return this.listeners.push(n),this.onSubscribe(),function(){e.listeners=e.listeners.filter(function(t){return t!==n}),e.onUnsubscribe()}},e.hasListeners=function(){return this.listeners.length>0},e.onSubscribe=function(){},e.onUnsubscribe=function(){},t}();function s(){return s=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)({}).hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},s.apply(null,arguments)}var u="undefined"==typeof window;function o(){}function a(t){return"number"==typeof t&&t>=0&&t!==1/0}function c(t){return Array.isArray(t)?t:[t]}function l(t,e){return Math.max(t+(e||0)-Date.now(),0)}function h(t,e,n){return R(t)?"function"==typeof e?s({},n,{queryKey:t,queryFn:e}):s({},e,{queryKey:t}):t}function f(t,e,n){return R(t)?[s({},e,{queryKey:t}),n]:[t||{},e]}function d(t,e){var n=t.active,i=t.exact,r=t.fetching,s=t.inactive,u=t.predicate,o=t.queryKey,a=t.stale;if(R(o))if(i){if(e.queryHash!==p(o,e.options))return!1}else if(!m(e.queryKey,o))return!1;var c=function(t,e){return!0===t&&!0===e||null==t&&null==e?"all":!1===t&&!1===e?"none":(null!=t?t:!e)?"active":"inactive"}(n,s);if("none"===c)return!1;if("all"!==c){var l=e.isActive();if("active"===c&&!l)return!1;if("inactive"===c&&l)return!1}return("boolean"!=typeof a||e.isStale()===a)&&(("boolean"!=typeof r||e.isFetching()===r)&&!(u&&!u(e)))}function v(t,e){var n=t.exact,i=t.fetching,r=t.predicate,s=t.mutationKey;if(R(s)){if(!e.options.mutationKey)return!1;if(n){if(y(e.options.mutationKey)!==y(s))return!1}else if(!m(e.options.mutationKey,s))return!1}return("boolean"!=typeof i||"loading"===e.state.status===i)&&!(r&&!r(e))}function p(t,e){return((null==e?void 0:e.queryKeyHashFn)||y)(t)}function y(t){var e,n=c(t);return e=n,JSON.stringify(e,function(t,e){return O(e)?Object.keys(e).sort().reduce(function(t,n){return t[n]=e[n],t},{}):e})}function m(t,e){return b(c(t),c(e))}function b(t,e){return t===e||typeof t==typeof e&&(!(!t||!e||"object"!=typeof t||"object"!=typeof e)&&!Object.keys(e).some(function(n){return!b(t[n],e[n])}))}function g(t,e){if(t===e)return t;var n=Array.isArray(t)&&Array.isArray(e);if(n||O(t)&&O(e)){for(var i=n?t.length:Object.keys(t).length,r=n?e:Object.keys(e),s=r.length,u=n?[]:{},o=0,a=0;a<s;a++){var c=n?a:r[a];u[c]=g(t[c],e[c]),u[c]===t[c]&&o++}return i===s&&o===i?t:u}return e}function O(t){if(!C(t))return!1;var e=t.constructor;if(void 0===e)return!0;var n=e.prototype;return!!C(n)&&!!n.hasOwnProperty("isPrototypeOf")}function C(t){return"[object Object]"===Object.prototype.toString.call(t)}function R(t){return"string"==typeof t||Array.isArray(t)}function S(t){Promise.resolve().then(t).catch(function(t){return setTimeout(function(){throw t})})}function q(){if("function"==typeof AbortController)return new AbortController}var P=new(function(t){function e(){var e;return(e=t.call(this)||this).setup=function(t){var e;if(!u&&(null==(e=window)?void 0:e.addEventListener)){var n=function(){return t()};return window.addEventListener("visibilitychange",n,!1),window.addEventListener("focus",n,!1),function(){window.removeEventListener("visibilitychange",n),window.removeEventListener("focus",n)}}},e}i(e,t);var n=e.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){var t;this.hasListeners()||(null==(t=this.cleanup)||t.call(this),this.cleanup=void 0)},n.setEventListener=function(t){var e,n=this;this.setup=t,null==(e=this.cleanup)||e.call(this),this.cleanup=t(function(t){"boolean"==typeof t?n.setFocused(t):n.onFocus()})},n.setFocused=function(t){this.focused=t,t&&this.onFocus()},n.onFocus=function(){this.listeners.forEach(function(t){t()})},n.isFocused=function(){return"boolean"==typeof this.focused?this.focused:"undefined"==typeof document||[void 0,"visible","prerender"].includes(document.visibilityState)},e}(r)),F=new(function(t){function e(){var e;return(e=t.call(this)||this).setup=function(t){var e;if(!u&&(null==(e=window)?void 0:e.addEventListener)){var n=function(){return t()};return window.addEventListener("online",n,!1),window.addEventListener("offline",n,!1),function(){window.removeEventListener("online",n),window.removeEventListener("offline",n)}}},e}i(e,t);var n=e.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){var t;this.hasListeners()||(null==(t=this.cleanup)||t.call(this),this.cleanup=void 0)},n.setEventListener=function(t){var e,n=this;this.setup=t,null==(e=this.cleanup)||e.call(this),this.cleanup=t(function(t){"boolean"==typeof t?n.setOnline(t):n.onOnline()})},n.setOnline=function(t){this.online=t,t&&this.onOnline()},n.onOnline=function(){this.listeners.forEach(function(t){t()})},n.isOnline=function(){return"boolean"==typeof this.online?this.online:"undefined"==typeof navigator||void 0===navigator.onLine||navigator.onLine},e}(r));function E(t){return Math.min(1e3*Math.pow(2,t),3e4)}function Q(t){return"function"==typeof(null==t?void 0:t.cancel)}var w=function(t){this.revert=null==t?void 0:t.revert,this.silent=null==t?void 0:t.silent};function M(t){return t instanceof w}var A=function(t){var e,n,i,r,s=this,u=!1;this.abort=t.abort,this.cancel=function(t){return null==e?void 0:e(t)},this.cancelRetry=function(){u=!0},this.continueRetry=function(){u=!1},this.continue=function(){return null==n?void 0:n()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise(function(t,e){i=t,r=e});var o=function(e){s.isResolved||(s.isResolved=!0,null==t.onSuccess||t.onSuccess(e),null==n||n(),i(e))},a=function(e){s.isResolved||(s.isResolved=!0,null==t.onError||t.onError(e),null==n||n(),r(e))};!function i(){if(!s.isResolved){var r;try{r=t.fn()}catch(c){r=Promise.reject(c)}e=function(t){if(!s.isResolved&&(a(new w(t)),null==s.abort||s.abort(),Q(r)))try{r.cancel()}catch(e){}},s.isTransportCancelable=Q(r),Promise.resolve(r).then(o).catch(function(e){var r,o;if(!s.isResolved){var c,l=null!=(r=t.retry)?r:3,h=null!=(o=t.retryDelay)?o:E,f="function"==typeof h?h(s.failureCount,e):h,d=!0===l||"number"==typeof l&&s.failureCount<l||"function"==typeof l&&l(s.failureCount,e);if(!u&&d)s.failureCount++,null==t.onFail||t.onFail(s.failureCount,e),(c=f,new Promise(function(t){setTimeout(t,c)})).then(function(){if(!P.isFocused()||!F.isOnline())return new Promise(function(e){n=e,s.isPaused=!0,null==t.onPause||t.onPause()}).then(function(){n=void 0,s.isPaused=!1,null==t.onContinue||t.onContinue()})}).then(function(){u?a(e):i()});else a(e)}})}}()},D=new(function(){function t(){this.queue=[],this.transactions=0,this.notifyFn=function(t){t()},this.batchNotifyFn=function(t){t()}}var e=t.prototype;return e.batch=function(t){var e;this.transactions++;try{e=t()}finally{this.transactions--,this.transactions||this.flush()}return e},e.schedule=function(t){var e=this;this.transactions?this.queue.push(t):S(function(){e.notifyFn(t)})},e.batchCalls=function(t){var e=this;return function(){for(var n=arguments.length,i=new Array(n),r=0;r<n;r++)i[r]=arguments[r];e.schedule(function(){t.apply(void 0,i)})}},e.flush=function(){var t=this,e=this.queue;this.queue=[],e.length&&S(function(){t.batchNotifyFn(function(){e.forEach(function(e){t.notifyFn(e)})})})},e.setNotifyFunction=function(t){this.notifyFn=t},e.setBatchNotifyFunction=function(t){this.batchNotifyFn=t},t}()),x=console;function T(){return x}var I=function(){function t(t){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.cache=t.cache,this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.initialState=t.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=t.meta,this.scheduleGc()}var e=t.prototype;return e.setOptions=function(t){var e;this.options=s({},this.defaultOptions,t),this.meta=null==t?void 0:t.meta,this.cacheTime=Math.max(this.cacheTime||0,null!=(e=this.options.cacheTime)?e:3e5)},e.setDefaultOptions=function(t){this.defaultOptions=t},e.scheduleGc=function(){var t=this;this.clearGcTimeout(),a(this.cacheTime)&&(this.gcTimeout=setTimeout(function(){t.optionalRemove()},this.cacheTime))},e.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},e.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},e.setData=function(t,e){var n,i,r=this.state.data,s=function(t,e){return"function"==typeof t?t(e):t}(t,r);return(null==(n=(i=this.options).isDataEqual)?void 0:n.call(i,r,s))?s=r:!1!==this.options.structuralSharing&&(s=g(r,s)),this.dispatch({data:s,type:"success",dataUpdatedAt:null==e?void 0:e.updatedAt}),s},e.setState=function(t,e){this.dispatch({type:"setState",state:t,setStateOptions:e})},e.cancel=function(t){var e,n=this.promise;return null==(e=this.retryer)||e.cancel(t),n?n.then(o).catch(o):Promise.resolve()},e.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},e.reset=function(){this.destroy(),this.setState(this.initialState)},e.isActive=function(){return this.observers.some(function(t){return!1!==t.options.enabled})},e.isFetching=function(){return this.state.isFetching},e.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(function(t){return t.getCurrentResult().isStale})},e.isStaleByTime=function(t){return void 0===t&&(t=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!l(this.state.dataUpdatedAt,t)},e.onFocus=function(){var t,e=this.observers.find(function(t){return t.shouldFetchOnWindowFocus()});e&&e.refetch(),null==(t=this.retryer)||t.continue()},e.onOnline=function(){var t,e=this.observers.find(function(t){return t.shouldFetchOnReconnect()});e&&e.refetch(),null==(t=this.retryer)||t.continue()},e.addObserver=function(t){-1===this.observers.indexOf(t)&&(this.observers.push(t),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:t}))},e.removeObserver=function(t){-1!==this.observers.indexOf(t)&&(this.observers=this.observers.filter(function(e){return e!==t}),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:t}))},e.getObserversCount=function(){return this.observers.length},e.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},e.fetch=function(t,e){var n,i,r,s=this;if(this.state.isFetching)if(this.state.dataUpdatedAt&&(null==e?void 0:e.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var u;return null==(u=this.retryer)||u.continueRetry(),this.promise}if(t&&this.setOptions(t),!this.options.queryFn){var o=this.observers.find(function(t){return t.options.queryFn});o&&this.setOptions(o.options)}var a=c(this.queryKey),l=q(),h={queryKey:a,pageParam:void 0,meta:this.meta};Object.defineProperty(h,"signal",{enumerable:!0,get:function(){if(l)return s.abortSignalConsumed=!0,l.signal}});var f,d,v={fetchOptions:e,options:this.options,queryKey:a,state:this.state,fetchFn:function(){return s.options.queryFn?(s.abortSignalConsumed=!1,s.options.queryFn(h)):Promise.reject("Missing queryFn")},meta:this.meta};(null==(n=this.options.behavior)?void 0:n.onFetch)&&(null==(f=this.options.behavior)||f.onFetch(v));(this.revertState=this.state,this.state.isFetching&&this.state.fetchMeta===(null==(i=v.fetchOptions)?void 0:i.meta))||this.dispatch({type:"fetch",meta:null==(d=v.fetchOptions)?void 0:d.meta});return this.retryer=new A({fn:v.fetchFn,abort:null==l||null==(r=l.abort)?void 0:r.bind(l),onSuccess:function(t){s.setData(t),null==s.cache.config.onSuccess||s.cache.config.onSuccess(t,s),0===s.cacheTime&&s.optionalRemove()},onError:function(t){M(t)&&t.silent||s.dispatch({type:"error",error:t}),M(t)||(null==s.cache.config.onError||s.cache.config.onError(t,s),T().error(t)),0===s.cacheTime&&s.optionalRemove()},onFail:function(){s.dispatch({type:"failed"})},onPause:function(){s.dispatch({type:"pause"})},onContinue:function(){s.dispatch({type:"continue"})},retry:v.options.retry,retryDelay:v.options.retryDelay}),this.promise=this.retryer.promise,this.promise},e.dispatch=function(t){var e=this;this.state=this.reducer(this.state,t),D.batch(function(){e.observers.forEach(function(e){e.onQueryUpdate(t)}),e.cache.notify({query:e,type:"queryUpdated",action:t})})},e.getDefaultState=function(t){var e="function"==typeof t.initialData?t.initialData():t.initialData,n=void 0!==t.initialData?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0,i=void 0!==e;return{data:e,dataUpdateCount:0,dataUpdatedAt:i?null!=n?n:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:i?"success":"idle"}},e.reducer=function(t,e){var n,i;switch(e.type){case"failed":return s({},t,{fetchFailureCount:t.fetchFailureCount+1});case"pause":return s({},t,{isPaused:!0});case"continue":return s({},t,{isPaused:!1});case"fetch":return s({},t,{fetchFailureCount:0,fetchMeta:null!=(n=e.meta)?n:null,isFetching:!0,isPaused:!1},!t.dataUpdatedAt&&{error:null,status:"loading"});case"success":return s({},t,{data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:null!=(i=e.dataUpdatedAt)?i:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var r=e.error;return M(r)&&r.revert&&this.revertState?s({},this.revertState):s({},t,{error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return s({},t,{isInvalidated:!0});case"setState":return s({},t,e.state);default:return t}},t}(),U=function(t){function e(e){var n;return(n=t.call(this)||this).config=e||{},n.queries=[],n.queriesMap={},n}i(e,t);var n=e.prototype;return n.build=function(t,e,n){var i,r=e.queryKey,s=null!=(i=e.queryHash)?i:p(r,e),u=this.get(s);return u||(u=new I({cache:this,queryKey:r,queryHash:s,options:t.defaultQueryOptions(e),state:n,defaultOptions:t.getQueryDefaults(r),meta:e.meta}),this.add(u)),u},n.add=function(t){this.queriesMap[t.queryHash]||(this.queriesMap[t.queryHash]=t,this.queries.push(t),this.notify({type:"queryAdded",query:t}))},n.remove=function(t){var e=this.queriesMap[t.queryHash];e&&(t.destroy(),this.queries=this.queries.filter(function(e){return e!==t}),e===t&&delete this.queriesMap[t.queryHash],this.notify({type:"queryRemoved",query:t}))},n.clear=function(){var t=this;D.batch(function(){t.queries.forEach(function(e){t.remove(e)})})},n.get=function(t){return this.queriesMap[t]},n.getAll=function(){return this.queries},n.find=function(t,e){var n=f(t,e)[0];return void 0===n.exact&&(n.exact=!0),this.queries.find(function(t){return d(n,t)})},n.findAll=function(t,e){var n=f(t,e)[0];return Object.keys(n).length>0?this.queries.filter(function(t){return d(n,t)}):this.queries},n.notify=function(t){var e=this;D.batch(function(){e.listeners.forEach(function(e){e(t)})})},n.onFocus=function(){var t=this;D.batch(function(){t.queries.forEach(function(t){t.onFocus()})})},n.onOnline=function(){var t=this;D.batch(function(){t.queries.forEach(function(t){t.onOnline()})})},e}(r),K=function(){function t(t){this.options=s({},t.defaultOptions,t.options),this.mutationId=t.mutationId,this.mutationCache=t.mutationCache,this.observers=[],this.state=t.state||{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0},this.meta=t.meta}var e=t.prototype;return e.setState=function(t){this.dispatch({type:"setState",state:t})},e.addObserver=function(t){-1===this.observers.indexOf(t)&&this.observers.push(t)},e.removeObserver=function(t){this.observers=this.observers.filter(function(e){return e!==t})},e.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(o).catch(o)):Promise.resolve()},e.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},e.execute=function(){var t,e=this,n="loading"===this.state.status,i=Promise.resolve();return n||(this.dispatch({type:"loading",variables:this.options.variables}),i=i.then(function(){null==e.mutationCache.config.onMutate||e.mutationCache.config.onMutate(e.state.variables,e)}).then(function(){return null==e.options.onMutate?void 0:e.options.onMutate(e.state.variables)}).then(function(t){t!==e.state.context&&e.dispatch({type:"loading",context:t,variables:e.state.variables})})),i.then(function(){return e.executeMutation()}).then(function(n){t=n,null==e.mutationCache.config.onSuccess||e.mutationCache.config.onSuccess(t,e.state.variables,e.state.context,e)}).then(function(){return null==e.options.onSuccess?void 0:e.options.onSuccess(t,e.state.variables,e.state.context)}).then(function(){return null==e.options.onSettled?void 0:e.options.onSettled(t,null,e.state.variables,e.state.context)}).then(function(){return e.dispatch({type:"success",data:t}),t}).catch(function(t){return null==e.mutationCache.config.onError||e.mutationCache.config.onError(t,e.state.variables,e.state.context,e),T().error(t),Promise.resolve().then(function(){return null==e.options.onError?void 0:e.options.onError(t,e.state.variables,e.state.context)}).then(function(){return null==e.options.onSettled?void 0:e.options.onSettled(void 0,t,e.state.variables,e.state.context)}).then(function(){throw e.dispatch({type:"error",error:t}),t})})},e.executeMutation=function(){var t,e=this;return this.retryer=new A({fn:function(){return e.options.mutationFn?e.options.mutationFn(e.state.variables):Promise.reject("No mutationFn found")},onFail:function(){e.dispatch({type:"failed"})},onPause:function(){e.dispatch({type:"pause"})},onContinue:function(){e.dispatch({type:"continue"})},retry:null!=(t=this.options.retry)?t:0,retryDelay:this.options.retryDelay}),this.retryer.promise},e.dispatch=function(t){var e=this;this.state=function(t,e){switch(e.type){case"failed":return s({},t,{failureCount:t.failureCount+1});case"pause":return s({},t,{isPaused:!0});case"continue":return s({},t,{isPaused:!1});case"loading":return s({},t,{context:e.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:e.variables});case"success":return s({},t,{data:e.data,error:null,status:"success",isPaused:!1});case"error":return s({},t,{data:void 0,error:e.error,failureCount:t.failureCount+1,isPaused:!1,status:"error"});case"setState":return s({},t,e.state);default:return t}}(this.state,t),D.batch(function(){e.observers.forEach(function(e){e.onMutationUpdate(t)}),e.mutationCache.notify(e)})},t}();var L=function(t){function e(e){var n;return(n=t.call(this)||this).config=e||{},n.mutations=[],n.mutationId=0,n}i(e,t);var n=e.prototype;return n.build=function(t,e,n){var i=new K({mutationCache:this,mutationId:++this.mutationId,options:t.defaultMutationOptions(e),state:n,defaultOptions:e.mutationKey?t.getMutationDefaults(e.mutationKey):void 0,meta:e.meta});return this.add(i),i},n.add=function(t){this.mutations.push(t),this.notify(t)},n.remove=function(t){this.mutations=this.mutations.filter(function(e){return e!==t}),t.cancel(),this.notify(t)},n.clear=function(){var t=this;D.batch(function(){t.mutations.forEach(function(e){t.remove(e)})})},n.getAll=function(){return this.mutations},n.find=function(t){return void 0===t.exact&&(t.exact=!0),this.mutations.find(function(e){return v(t,e)})},n.findAll=function(t){return this.mutations.filter(function(e){return v(t,e)})},n.notify=function(t){var e=this;D.batch(function(){e.listeners.forEach(function(e){e(t)})})},n.onFocus=function(){this.resumePausedMutations()},n.onOnline=function(){this.resumePausedMutations()},n.resumePausedMutations=function(){var t=this.mutations.filter(function(t){return t.state.isPaused});return D.batch(function(){return t.reduce(function(t,e){return t.then(function(){return e.continue().catch(o)})},Promise.resolve())})},e}(r);function j(t,e){return null==t.getNextPageParam?void 0:t.getNextPageParam(e[e.length-1],e)}var k=function(){function t(t){void 0===t&&(t={}),this.queryCache=t.queryCache||new U,this.mutationCache=t.mutationCache||new L,this.defaultOptions=t.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var e=t.prototype;return e.mount=function(){var t=this;this.unsubscribeFocus=P.subscribe(function(){P.isFocused()&&F.isOnline()&&(t.mutationCache.onFocus(),t.queryCache.onFocus())}),this.unsubscribeOnline=F.subscribe(function(){P.isFocused()&&F.isOnline()&&(t.mutationCache.onOnline(),t.queryCache.onOnline())})},e.unmount=function(){var t,e;null==(t=this.unsubscribeFocus)||t.call(this),null==(e=this.unsubscribeOnline)||e.call(this)},e.isFetching=function(t,e){var n=f(t,e)[0];return n.fetching=!0,this.queryCache.findAll(n).length},e.isMutating=function(t){return this.mutationCache.findAll(s({},t,{fetching:!0})).length},e.getQueryData=function(t,e){var n;return null==(n=this.queryCache.find(t,e))?void 0:n.state.data},e.getQueriesData=function(t){return this.getQueryCache().findAll(t).map(function(t){return[t.queryKey,t.state.data]})},e.setQueryData=function(t,e,n){var i=h(t),r=this.defaultQueryOptions(i);return this.queryCache.build(this,r).setData(e,n)},e.setQueriesData=function(t,e,n){var i=this;return D.batch(function(){return i.getQueryCache().findAll(t).map(function(t){var r=t.queryKey;return[r,i.setQueryData(r,e,n)]})})},e.getQueryState=function(t,e){var n;return null==(n=this.queryCache.find(t,e))?void 0:n.state},e.removeQueries=function(t,e){var n=f(t,e)[0],i=this.queryCache;D.batch(function(){i.findAll(n).forEach(function(t){i.remove(t)})})},e.resetQueries=function(t,e,n){var i=this,r=f(t,e,n),u=r[0],o=r[1],a=this.queryCache,c=s({},u,{active:!0});return D.batch(function(){return a.findAll(u).forEach(function(t){t.reset()}),i.refetchQueries(c,o)})},e.cancelQueries=function(t,e,n){var i=this,r=f(t,e,n),s=r[0],u=r[1],a=void 0===u?{}:u;void 0===a.revert&&(a.revert=!0);var c=D.batch(function(){return i.queryCache.findAll(s).map(function(t){return t.cancel(a)})});return Promise.all(c).then(o).catch(o)},e.invalidateQueries=function(t,e,n){var i,r,u,o=this,a=f(t,e,n),c=a[0],l=a[1],h=s({},c,{active:null==(i=null!=(r=c.refetchActive)?r:c.active)||i,inactive:null!=(u=c.refetchInactive)&&u});return D.batch(function(){return o.queryCache.findAll(c).forEach(function(t){t.invalidate()}),o.refetchQueries(h,l)})},e.refetchQueries=function(t,e,n){var i=this,r=f(t,e,n),u=r[0],a=r[1],c=D.batch(function(){return i.queryCache.findAll(u).map(function(t){return t.fetch(void 0,s({},a,{meta:{refetchPage:null==u?void 0:u.refetchPage}}))})}),l=Promise.all(c).then(o);return(null==a?void 0:a.throwOnError)||(l=l.catch(o)),l},e.fetchQuery=function(t,e,n){var i=h(t,e,n),r=this.defaultQueryOptions(i);void 0===r.retry&&(r.retry=!1);var s=this.queryCache.build(this,r);return s.isStaleByTime(r.staleTime)?s.fetch(r):Promise.resolve(s.state.data)},e.prefetchQuery=function(t,e,n){return this.fetchQuery(t,e,n).then(o).catch(o)},e.fetchInfiniteQuery=function(t,e,n){var i=h(t,e,n);return i.behavior={onFetch:function(t){t.fetchFn=function(){var e,n,i,r,s,u,o,a,c,l=null==(e=t.fetchOptions)||null==(n=e.meta)?void 0:n.refetchPage,h=null==(i=t.fetchOptions)||null==(r=i.meta)?void 0:r.fetchMore,f=null==h?void 0:h.pageParam,d="forward"===(null==h?void 0:h.direction),v="backward"===(null==h?void 0:h.direction),p=(null==(s=t.state.data)?void 0:s.pages)||[],y=(null==(u=t.state.data)?void 0:u.pageParams)||[],m=q(),b=null==m?void 0:m.signal,g=y,O=!1,C=t.options.queryFn||function(){return Promise.reject("Missing queryFn")},R=function(t,e,n,i){return g=i?[e].concat(g):[].concat(g,[e]),i?[n].concat(t):[].concat(t,[n])},S=function(e,n,i,r){if(O)return Promise.reject("Cancelled");if(void 0===i&&!n&&e.length)return Promise.resolve(e);var s={queryKey:t.queryKey,signal:b,pageParam:i,meta:t.meta},u=C(s),o=Promise.resolve(u).then(function(t){return R(e,i,t,r)});return Q(u)&&(o.cancel=u.cancel),o};if(p.length)if(d){var P=void 0!==f,F=P?f:j(t.options,p);o=S(p,P,F)}else if(v){var E=void 0!==f,w=E?f:(a=t.options,c=p,null==a.getPreviousPageParam?void 0:a.getPreviousPageParam(c[0],c));o=S(p,E,w,!0)}else!function(){g=[];var e=void 0===t.options.getNextPageParam,n=!l||!p[0]||l(p[0],0,p);o=n?S([],e,y[0]):Promise.resolve(R([],y[0],p[0]));for(var i=function(n){o=o.then(function(i){if(!l||!p[n]||l(p[n],n,p)){var r=e?y[n]:j(t.options,i);return S(i,e,r)}return Promise.resolve(R(i,y[n],p[n]))})},r=1;r<p.length;r++)i(r)}();else o=S([]);var M=o.then(function(t){return{pages:t,pageParams:g}});return M.cancel=function(){O=!0,null==m||m.abort(),Q(o)&&o.cancel()},M}}},this.fetchQuery(i)},e.prefetchInfiniteQuery=function(t,e,n){return this.fetchInfiniteQuery(t,e,n).then(o).catch(o)},e.cancelMutations=function(){var t=this,e=D.batch(function(){return t.mutationCache.getAll().map(function(t){return t.cancel()})});return Promise.all(e).then(o).catch(o)},e.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},e.executeMutation=function(t){return this.mutationCache.build(this,t).execute()},e.getQueryCache=function(){return this.queryCache},e.getMutationCache=function(){return this.mutationCache},e.getDefaultOptions=function(){return this.defaultOptions},e.setDefaultOptions=function(t){this.defaultOptions=t},e.setQueryDefaults=function(t,e){var n=this.queryDefaults.find(function(e){return y(t)===y(e.queryKey)});n?n.defaultOptions=e:this.queryDefaults.push({queryKey:t,defaultOptions:e})},e.getQueryDefaults=function(t){var e;return t?null==(e=this.queryDefaults.find(function(e){return m(t,e.queryKey)}))?void 0:e.defaultOptions:void 0},e.setMutationDefaults=function(t,e){var n=this.mutationDefaults.find(function(e){return y(t)===y(e.mutationKey)});n?n.defaultOptions=e:this.mutationDefaults.push({mutationKey:t,defaultOptions:e})},e.getMutationDefaults=function(t){var e;return t?null==(e=this.mutationDefaults.find(function(e){return m(t,e.mutationKey)}))?void 0:e.defaultOptions:void 0},e.defaultQueryOptions=function(t){if(null==t?void 0:t._defaulted)return t;var e=s({},this.defaultOptions.queries,this.getQueryDefaults(null==t?void 0:t.queryKey),t,{_defaulted:!0});return!e.queryHash&&e.queryKey&&(e.queryHash=p(e.queryKey,e)),e},e.defaultQueryObserverOptions=function(t){return this.defaultQueryOptions(t)},e.defaultMutationOptions=function(t){return(null==t?void 0:t._defaulted)?t:s({},this.defaultOptions.mutations,this.getMutationDefaults(null==t?void 0:t.mutationKey),t,{_defaulted:!0})},e.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},t}(),N=function(t){function e(e,n){var i;return(i=t.call(this)||this).client=e,i.options=n,i.trackedProps=[],i.selectError=null,i.bindMethods(),i.setOptions(n),i}i(e,t);var n=e.prototype;return n.bindMethods=function(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},n.onSubscribe=function(){1===this.listeners.length&&(this.currentQuery.addObserver(this),H(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},n.onUnsubscribe=function(){this.listeners.length||this.destroy()},n.shouldFetchOnReconnect=function(){return B(this.currentQuery,this.options,this.options.refetchOnReconnect)},n.shouldFetchOnWindowFocus=function(){return B(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},n.destroy=function(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},n.setOptions=function(t,e){var n=this.options,i=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled)throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=n.queryKey),this.updateQuery();var r=this.hasListeners();r&&_(this.currentQuery,i,this.options,n)&&this.executeFetch(),this.updateResult(e),!r||this.currentQuery===i&&this.options.enabled===n.enabled&&this.options.staleTime===n.staleTime||this.updateStaleTimeout();var s=this.computeRefetchInterval();!r||this.currentQuery===i&&this.options.enabled===n.enabled&&s===this.currentRefetchInterval||this.updateRefetchInterval(s)},n.getOptimisticResult=function(t){var e=this.client.defaultQueryObserverOptions(t),n=this.client.getQueryCache().build(this.client,e);return this.createResult(n,e)},n.getCurrentResult=function(){return this.currentResult},n.trackResult=function(t,e){var n=this,i={},r=function(t){n.trackedProps.includes(t)||n.trackedProps.push(t)};return Object.keys(t).forEach(function(e){Object.defineProperty(i,e,{configurable:!1,enumerable:!0,get:function(){return r(e),t[e]}})}),(e.useErrorBoundary||e.suspense)&&r("error"),i},n.getNextResult=function(t){var e=this;return new Promise(function(n,i){var r=e.subscribe(function(e){e.isFetching||(r(),e.isError&&(null==t?void 0:t.throwOnError)?i(e.error):n(e))})})},n.getCurrentQuery=function(){return this.currentQuery},n.remove=function(){this.client.getQueryCache().remove(this.currentQuery)},n.refetch=function(t){return this.fetch(s({},t,{meta:{refetchPage:null==t?void 0:t.refetchPage}}))},n.fetchOptimistic=function(t){var e=this,n=this.client.defaultQueryObserverOptions(t),i=this.client.getQueryCache().build(this.client,n);return i.fetch().then(function(){return e.createResult(i,n)})},n.fetch=function(t){var e=this;return this.executeFetch(t).then(function(){return e.updateResult(),e.currentResult})},n.executeFetch=function(t){this.updateQuery();var e=this.currentQuery.fetch(this.options,t);return(null==t?void 0:t.throwOnError)||(e=e.catch(o)),e},n.updateStaleTimeout=function(){var t=this;if(this.clearStaleTimeout(),!u&&!this.currentResult.isStale&&a(this.options.staleTime)){var e=l(this.currentResult.dataUpdatedAt,this.options.staleTime)+1;this.staleTimeoutId=setTimeout(function(){t.currentResult.isStale||t.updateResult()},e)}},n.computeRefetchInterval=function(){var t;return"function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.currentResult.data,this.currentQuery):null!=(t=this.options.refetchInterval)&&t},n.updateRefetchInterval=function(t){var e=this;this.clearRefetchInterval(),this.currentRefetchInterval=t,!u&&!1!==this.options.enabled&&a(this.currentRefetchInterval)&&0!==this.currentRefetchInterval&&(this.refetchIntervalId=setInterval(function(){(e.options.refetchIntervalInBackground||P.isFocused())&&e.executeFetch()},this.currentRefetchInterval))},n.updateTimers=function(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},n.clearTimers=function(){this.clearStaleTimeout(),this.clearRefetchInterval()},n.clearStaleTimeout=function(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},n.clearRefetchInterval=function(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},n.createResult=function(t,e){var n,i=this.currentQuery,r=this.options,s=this.currentResult,u=this.currentResultState,o=this.currentResultOptions,a=t!==i,c=a?t.state:this.currentQueryInitialState,l=a?this.currentResult:this.previousQueryResult,h=t.state,f=h.dataUpdatedAt,d=h.error,v=h.errorUpdatedAt,p=h.isFetching,y=h.status,m=!1,b=!1;if(e.optimisticResults){var O=this.hasListeners(),C=!O&&H(t,e),R=O&&_(t,i,e,r);(C||R)&&(p=!0,f||(y="loading"))}if(e.keepPreviousData&&!h.dataUpdateCount&&(null==l?void 0:l.isSuccess)&&"error"!==y)n=l.data,f=l.dataUpdatedAt,y=l.status,m=!0;else if(e.select&&void 0!==h.data)if(s&&h.data===(null==u?void 0:u.data)&&e.select===this.selectFn)n=this.selectResult;else try{this.selectFn=e.select,n=e.select(h.data),!1!==e.structuralSharing&&(n=g(null==s?void 0:s.data,n)),this.selectResult=n,this.selectError=null}catch(q){T().error(q),this.selectError=q}else n=h.data;if(void 0!==e.placeholderData&&void 0===n&&("loading"===y||"idle"===y)){var S;if((null==s?void 0:s.isPlaceholderData)&&e.placeholderData===(null==o?void 0:o.placeholderData))S=s.data;else if(S="function"==typeof e.placeholderData?e.placeholderData():e.placeholderData,e.select&&void 0!==S)try{S=e.select(S),!1!==e.structuralSharing&&(S=g(null==s?void 0:s.data,S)),this.selectError=null}catch(q){T().error(q),this.selectError=q}void 0!==S&&(y="success",n=S,b=!0)}return this.selectError&&(d=this.selectError,n=this.selectResult,v=Date.now(),y="error"),{status:y,isLoading:"loading"===y,isSuccess:"success"===y,isError:"error"===y,isIdle:"idle"===y,data:n,dataUpdatedAt:f,error:d,errorUpdatedAt:v,failureCount:h.fetchFailureCount,errorUpdateCount:h.errorUpdateCount,isFetched:h.dataUpdateCount>0||h.errorUpdateCount>0,isFetchedAfterMount:h.dataUpdateCount>c.dataUpdateCount||h.errorUpdateCount>c.errorUpdateCount,isFetching:p,isRefetching:p&&"loading"!==y,isLoadingError:"error"===y&&0===h.dataUpdatedAt,isPlaceholderData:b,isPreviousData:m,isRefetchError:"error"===y&&0!==h.dataUpdatedAt,isStale:G(t,e),refetch:this.refetch,remove:this.remove}},n.shouldNotifyListeners=function(t,e){if(!e)return!0;var n=this.options,i=n.notifyOnChangeProps,r=n.notifyOnChangePropsExclusions;if(!i&&!r)return!0;if("tracked"===i&&!this.trackedProps.length)return!0;var s="tracked"===i?this.trackedProps:i;return Object.keys(t).some(function(n){var i=n,u=t[i]!==e[i],o=null==s?void 0:s.some(function(t){return t===n}),a=null==r?void 0:r.some(function(t){return t===n});return u&&!a&&(!s||o)})},n.updateResult=function(t){var e=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!function(t,e){if(t&&!e||e&&!t)return!1;for(var n in t)if(t[n]!==e[n])return!1;return!0}(this.currentResult,e)){var n={cache:!0};!1!==(null==t?void 0:t.listeners)&&this.shouldNotifyListeners(this.currentResult,e)&&(n.listeners=!0),this.notify(s({},n,t))}},n.updateQuery=function(){var t=this.client.getQueryCache().build(this.client,this.options);if(t!==this.currentQuery){var e=this.currentQuery;this.currentQuery=t,this.currentQueryInitialState=t.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(null==e||e.removeObserver(this),t.addObserver(this))}},n.onQueryUpdate=function(t){var e={};"success"===t.type?e.onSuccess=!0:"error"!==t.type||M(t.error)||(e.onError=!0),this.updateResult(e),this.hasListeners()&&this.updateTimers()},n.notify=function(t){var e=this;D.batch(function(){t.onSuccess?(null==e.options.onSuccess||e.options.onSuccess(e.currentResult.data),null==e.options.onSettled||e.options.onSettled(e.currentResult.data,null)):t.onError&&(null==e.options.onError||e.options.onError(e.currentResult.error),null==e.options.onSettled||e.options.onSettled(void 0,e.currentResult.error)),t.listeners&&e.listeners.forEach(function(t){t(e.currentResult)}),t.cache&&e.client.getQueryCache().notify({query:e.currentQuery,type:"observerResultsUpdated"})})},e}(r);function H(t,e){return function(t,e){return!(!1===e.enabled||t.state.dataUpdatedAt||"error"===t.state.status&&!1===e.retryOnMount)}(t,e)||t.state.dataUpdatedAt>0&&B(t,e,e.refetchOnMount)}function B(t,e,n){if(!1!==e.enabled){var i="function"==typeof n?n(t):n;return"always"===i||!1!==i&&G(t,e)}return!1}function _(t,e,n,i){return!1!==n.enabled&&(t!==e||!1===i.enabled)&&(!n.suspense||"error"!==t.state.status)&&G(t,n)}function G(t,e){return t.isStaleByTime(e.staleTime)}var W=function(t){function e(e,n){var i;return(i=t.call(this)||this).client=e,i.setOptions(n),i.bindMethods(),i.updateResult(),i}i(e,t);var n=e.prototype;return n.bindMethods=function(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)},n.setOptions=function(t){this.options=this.client.defaultMutationOptions(t)},n.onUnsubscribe=function(){var t;this.listeners.length||(null==(t=this.currentMutation)||t.removeObserver(this))},n.onMutationUpdate=function(t){this.updateResult();var e={listeners:!0};"success"===t.type?e.onSuccess=!0:"error"===t.type&&(e.onError=!0),this.notify(e)},n.getCurrentResult=function(){return this.currentResult},n.reset=function(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})},n.mutate=function(t,e){return this.mutateOptions=e,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,s({},this.options,{variables:void 0!==t?t:this.options.variables})),this.currentMutation.addObserver(this),this.currentMutation.execute()},n.updateResult=function(){var t=this.currentMutation?this.currentMutation.state:{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0},e=s({},t,{isLoading:"loading"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset});this.currentResult=e},n.notify=function(t){var e=this;D.batch(function(){e.mutateOptions&&(t.onSuccess?(null==e.mutateOptions.onSuccess||e.mutateOptions.onSuccess(e.currentResult.data,e.currentResult.variables,e.currentResult.context),null==e.mutateOptions.onSettled||e.mutateOptions.onSettled(e.currentResult.data,null,e.currentResult.variables,e.currentResult.context)):t.onError&&(null==e.mutateOptions.onError||e.mutateOptions.onError(e.currentResult.error,e.currentResult.variables,e.currentResult.context),null==e.mutateOptions.onSettled||e.mutateOptions.onSettled(void 0,e.currentResult.error,e.currentResult.variables,e.currentResult.context))),t.listeners&&e.listeners.forEach(function(t){t(e.currentResult)})})},e}(r),J=t.unstable_batchedUpdates;D.setBatchNotifyFunction(J);var z=console;x=z;var V=e.createContext(void 0),X=e.createContext(!1);function Y(t){return t&&"undefined"!=typeof window?(window.ReactQueryClientContext||(window.ReactQueryClientContext=V),window.ReactQueryClientContext):V}var Z=function(){var t=e.useContext(Y(e.useContext(X)));if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},$=function(t){var n=t.client,i=t.contextSharing,r=void 0!==i&&i,s=t.children;e.useEffect(function(){return n.mount(),function(){n.unmount()}},[n]);var u=Y(r);return e.createElement(X.Provider,{value:r},e.createElement(u.Provider,{value:n},s))};var tt,et=e.createContext((tt=!1,{clearReset:function(){tt=!1},reset:function(){tt=!0},isReset:function(){return tt}}));function nt(t,e,n){return"function"==typeof e?e.apply(void 0,n):"boolean"==typeof e?e:!!t}function it(t,n,i){var r=e.useRef(!1),u=e.useState(0)[1],a=function(t,e,n){return R(t)?"function"==typeof e?s({},n,{mutationKey:t,mutationFn:e}):s({},e,{mutationKey:t}):"function"==typeof t?s({},e,{mutationFn:t}):s({},t)}(t,n,i),c=Z(),l=e.useRef();l.current?l.current.setOptions(a):l.current=new W(c,a);var h=l.current.getCurrentResult();e.useEffect(function(){r.current=!0;var t=l.current.subscribe(D.batchCalls(function(){r.current&&u(function(t){return t+1})}));return function(){r.current=!1,t()}},[]);var f=e.useCallback(function(t,e){l.current.mutate(t,e).catch(o)},[]);if(h.error&&nt(void 0,l.current.options.useErrorBoundary,[h.error]))throw h.error;return s({},h,{mutate:f,mutateAsync:h.mutate})}function rt(t,n){var i=e.useRef(!1),r=e.useState(0)[1],s=Z(),u=e.useContext(et),o=s.defaultQueryObserverOptions(t);o.optimisticResults=!0,o.onError&&(o.onError=D.batchCalls(o.onError)),o.onSuccess&&(o.onSuccess=D.batchCalls(o.onSuccess)),o.onSettled&&(o.onSettled=D.batchCalls(o.onSettled)),o.suspense&&("number"!=typeof o.staleTime&&(o.staleTime=1e3),0===o.cacheTime&&(o.cacheTime=1)),(o.suspense||o.useErrorBoundary)&&(u.isReset()||(o.retryOnMount=!1));var a=e.useState(function(){return new n(s,o)})[0],c=a.getOptimisticResult(o);if(e.useEffect(function(){i.current=!0,u.clearReset();var t=a.subscribe(D.batchCalls(function(){i.current&&r(function(t){return t+1})}));return a.updateResult(),function(){i.current=!1,t()}},[u,a]),e.useEffect(function(){a.setOptions(o,{listeners:!1})},[o,a]),o.suspense&&c.isLoading)throw a.fetchOptimistic(o).then(function(t){var e=t.data;null==o.onSuccess||o.onSuccess(e),null==o.onSettled||o.onSettled(e,null)}).catch(function(t){u.clearReset(),null==o.onError||o.onError(t),null==o.onSettled||o.onSettled(void 0,t)});if(c.isError&&!u.isReset()&&!c.isFetching&&nt(o.suspense,o.useErrorBoundary,[c.error,a.getCurrentQuery()]))throw c.error;return"tracked"===o.notifyOnChangeProps&&(c=a.trackResult(c,o)),c}function st(t,e,n){return rt(h(t,e,n),N)}export{k as Q,st as a,it as b,$ as c,Z as u};
