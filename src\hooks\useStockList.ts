import { useState, useCallback, useEffect } from 'react';
import { Stock } from '@/types';
import { validateStockCode, formatStockCode, isDuplicateCode } from '@/utils/validation';

interface UseStockListReturn {
  stocks: Stock[];
  addStock: (code: string) => Promise<{ success: boolean; message?: string }>;
  removeStock: (code: string) => void;
  clearAllStocks: () => void;
  isLoading: boolean;
  error: string | null;
}

const STORAGE_KEY = 'gupiao-stock-list';

/**
 * 股票列表管理Hook
 */
export function useStockList(): UseStockListReturn {
  const [stocks, setStocks] = useState<Stock[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 从localStorage加载股票列表
  useEffect(() => {
    try {
      const savedStocks = localStorage.getItem(STORAGE_KEY);
      if (savedStocks) {
        const parsedStocks = JSON.parse(savedStocks);
        setStocks(parsedStocks);
      }
    } catch (err) {
      console.error('加载股票列表失败:', err);
      setError('加载股票列表失败');
    }
  }, []);

  // 保存股票列表到localStorage
  const saveToStorage = useCallback((stockList: Stock[]) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(stockList));
    } catch (err) {
      console.error('保存股票列表失败:', err);
      setError('保存股票列表失败');
    }
  }, []);

  // 添加股票
  const addStock = useCallback(async (code: string): Promise<{ success: boolean; message?: string }> => {
    setIsLoading(true);
    setError(null);

    try {
      // 验证股票代码
      const validation = validateStockCode(code);
      if (!validation.isValid) {
        return { success: false, message: validation.message };
      }

      const formattedCode = formatStockCode(code);
      const existingCodes = stocks.map(stock => stock.code);

      // 检查重复
      if (isDuplicateCode(formattedCode, existingCodes)) {
        return { success: false, message: '股票代码已存在' };
      }

      // 创建新股票对象
      const newStock: Stock = {
        code: formattedCode,
        name: `股票${formattedCode}`, // 暂时使用代码作为名称，后续会从API获取真实名称
      };

      // 更新股票列表
      const updatedStocks = [...stocks, newStock];
      setStocks(updatedStocks);
      saveToStorage(updatedStocks);

      return { success: true, message: '股票添加成功' };
    } catch (err) {
      const errorMessage = '添加股票失败';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [stocks, saveToStorage]);

  // 删除股票
  const removeStock = useCallback((code: string) => {
    setError(null);
    
    try {
      const updatedStocks = stocks.filter(stock => stock.code !== code);
      setStocks(updatedStocks);
      saveToStorage(updatedStocks);
    } catch (err) {
      console.error('删除股票失败:', err);
      setError('删除股票失败');
    }
  }, [stocks, saveToStorage]);

  // 清空所有股票
  const clearAllStocks = useCallback(() => {
    setError(null);
    
    try {
      setStocks([]);
      saveToStorage([]);
    } catch (err) {
      console.error('清空股票列表失败:', err);
      setError('清空股票列表失败');
    }
  }, [saveToStorage]);

  return {
    stocks,
    addStock,
    removeStock,
    clearAllStocks,
    isLoading,
    error,
  };
}
